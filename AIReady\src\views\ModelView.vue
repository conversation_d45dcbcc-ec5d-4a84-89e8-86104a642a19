<template>
  <div class="model-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title gradient-text">AI模型</h1>
        <p class="page-subtitle">发现最新的AI模型，加速您的项目开发</p>
      </div>

      <!-- 搜索和筛选区域 -->
      <div class="search-section tech-card">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :md="8">
            <el-input
              v-model="searchQuery"
              placeholder="搜索模型..."
              size="large"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6">
            <el-select
              v-model="selectedCategory"
              placeholder="选择类别"
              size="large"
              clearable
              @change="handleCategoryChange"
            >
              <el-option
                v-for="category in categories"
                :key="category.value"
                :label="category.label"
                :value="category.value"
              />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6">
            <el-select v-model="sortBy" placeholder="排序方式" size="large" @change="handleSort">
              <el-option label="最新发布" value="latest" />
              <el-option label="下载最多" value="downloads" />
              <el-option label="评分最高" value="rating" />
              <el-option label="模型大小" value="size" />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="12" :md="4">
            <el-button
              type="primary"
              size="large"
              class="tech-button"
              style="width: 100%"
              @click="$router.push('/account')"
            >
              <el-icon><Plus /></el-icon>
              上传模型
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 模型列表 -->
      <div class="model-grid">
        <el-row :gutter="24">
          <el-col
            v-for="model in paginatedModels"
            :key="model.id"
            :xs="24"
            :sm="12"
            :lg="8"
            class="model-col"
          >
            <div class="model-card tech-card">
              <div class="model-header">
                <div class="model-icon">
                  <el-icon><Cpu /></el-icon>
                </div>
                <div class="model-meta">
                  <span class="model-category">{{ model.category }}</span>
                  <span class="model-size">{{ model.size }}</span>
                </div>
              </div>

              <h3 class="model-title">{{ model.name }}</h3>
              <p class="model-description">{{ model.description }}</p>

              <div class="model-performance">
                <div class="performance-item">
                  <span class="performance-label">准确率:</span>
                  <span class="performance-value">{{ model.accuracy }}</span>
                </div>
                <div class="performance-item">
                  <span class="performance-label">推理速度:</span>
                  <span class="performance-value">{{ model.speed }}</span>
                </div>
              </div>

              <div class="model-tags">
                <el-tag v-for="tag in model.tags" :key="tag" size="small" class="model-tag">
                  {{ tag }}
                </el-tag>
              </div>

              <div class="model-actions">
                <el-button type="primary" class="tech-button" @click="downloadModel(model)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                <el-button class="tech-button secondary" @click="tryModel(model)">
                  <el-icon><VideoPlay /></el-icon>
                  试用
                </el-button>
              </div>

              <div class="model-stats">
                <div class="stat-item">
                  <el-icon><Download /></el-icon>
                  <span>{{ model.downloads }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Star /></el-icon>
                  <span>{{ model.rating }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Calendar /></el-icon>
                  <span>{{ model.releaseDate }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 48]"
          :total="filteredModels.length"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Plus, Cpu, Download, VideoPlay, Star, Calendar } from '@element-plus/icons-vue'

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('')
const sortBy = ref('latest')
const currentPage = ref(1)
const pageSize = ref(12)

// 类别选项
const categories = ref([
  { label: '图像分类', value: 'image-classification' },
  { label: '目标检测', value: 'object-detection' },
  { label: '语言模型', value: 'language-model' },
  { label: '语音识别', value: 'speech-recognition' },
  { label: '推荐系统', value: 'recommendation' },
  { label: '其他', value: 'other' },
])

// 模拟模型数据
const models = ref([
  {
    id: 1,
    name: 'ResNet-50',
    description: '深度残差网络，在图像分类任务上表现优异，是计算机视觉领域的经典模型。',
    category: '图像分类',
    size: '98MB',
    accuracy: '76.1%',
    speed: '22ms',
    downloads: '89,234',
    rating: '4.7',
    releaseDate: '2024-01-15',
    tags: ['CNN', '残差网络', '图像分类'],
  },
  {
    id: 2,
    name: 'BERT-Base',
    description: '基于Transformer的双向编码器，在自然语言理解任务上取得突破性进展。',
    category: '语言模型',
    size: '440MB',
    accuracy: '84.3%',
    speed: '45ms',
    downloads: '156,789',
    rating: '4.8',
    releaseDate: '2024-01-20',
    tags: ['Transformer', 'NLP', '预训练'],
  },
  {
    id: 3,
    name: 'YOLOv8',
    description: '最新版本的YOLO目标检测模型，在速度和精度之间取得了很好的平衡。',
    category: '目标检测',
    size: '22MB',
    accuracy: '53.9%',
    speed: '8ms',
    downloads: '67,432',
    rating: '4.6',
    releaseDate: '2024-01-25',
    tags: ['YOLO', '实时检测', '目标检测'],
  },
])

// 计算属性
const filteredModels = computed(() => {
  let result = models.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(
      (model) =>
        model.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        model.description.toLowerCase().includes(searchQuery.value.toLowerCase()),
    )
  }

  // 类别过滤
  if (selectedCategory.value) {
    result = result.filter((model) => model.category === getCategoryLabel(selectedCategory.value))
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'downloads':
        return parseInt(b.downloads.replace(/,/g, '')) - parseInt(a.downloads.replace(/,/g, ''))
      case 'rating':
        return parseFloat(b.rating) - parseFloat(a.rating)
      case 'size':
        return parseSize(b.size) - parseSize(a.size)
      default: // latest
        return new Date(b.releaseDate) - new Date(a.releaseDate)
    }
  })

  return result
})

const paginatedModels = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredModels.value.slice(start, end)
})

// 方法
const getCategoryLabel = (value) => {
  const category = categories.value.find((cat) => cat.value === value)
  return category ? category.label : ''
}

const parseSize = (sizeStr) => {
  const num = parseFloat(sizeStr)
  if (sizeStr.includes('GB')) return num * 1024
  if (sizeStr.includes('MB')) return num
  if (sizeStr.includes('KB')) return num / 1024
  return 0
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleCategoryChange = () => {
  currentPage.value = 1
}

const handleSort = () => {
  currentPage.value = 1
}

const handleSizeChange = () => {
  currentPage.value = 1
}

const handleCurrentChange = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

const downloadModel = (model) => {
  ElMessage.success(`开始下载模型: ${model.name}`)
}

const tryModel = (model) => {
  ElMessage.info(`正在启动模型试用: ${model.name}`)
}

onMounted(() => {
  // 模拟加载更多数据
  for (let i = 4; i <= 30; i++) {
    models.value.push({
      id: i,
      name: `模型 ${i}`,
      description: `这是第${i}个AI模型的描述信息，具有优秀的性能表现。`,
      category: categories.value[i % categories.value.length].label,
      size: `${Math.floor(Math.random() * 500) + 10}MB`,
      accuracy: `${(Math.random() * 30 + 60).toFixed(1)}%`,
      speed: `${Math.floor(Math.random() * 50) + 5}ms`,
      downloads: `${Math.floor(Math.random() * 100000) + 1000}`,
      rating: (Math.random() * 2 + 3).toFixed(1),
      releaseDate: `2024-01-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      tags: ['标签1', '标签2', '标签3'].slice(0, Math.floor(Math.random() * 3) + 1),
    })
  }
})
</script>

<style scoped>
.model-page {
  background: var(--bg-primary);
  min-height: 100vh;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
}

.search-section {
  margin-bottom: 32px;
  padding: 24px;
}

.model-grid {
  margin-bottom: 40px;
}

.model-col {
  margin-bottom: 24px;
}

.model-card {
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.model-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px var(--shadow-color);
  border-color: var(--primary-color);
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.model-icon {
  font-size: 2rem;
  color: var(--secondary-color);
}

.model-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.model-category {
  background: var(--secondary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.model-size {
  color: var(--text-muted);
  font-size: 0.875rem;
}

.model-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  line-height: 1.4;
}

.model-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 16px;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.model-performance {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.performance-item:last-child {
  margin-bottom: 0;
}

.performance-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.performance-value {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 0.875rem;
}

.model-tags {
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.model-tag {
  background: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
}

.model-actions {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
}

.model-actions .el-button {
  flex: 1;
  font-size: 0.875rem;
  padding: 8px 16px;
}

.tech-button.secondary {
  background: transparent !important;
  border: 2px solid var(--secondary-color) !important;
  color: var(--secondary-color) !important;
}

.tech-button.secondary:hover {
  background: var(--secondary-color) !important;
  color: white !important;
}

.model-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-muted);
  font-size: 0.875rem;
}

.stat-item .el-icon {
  font-size: 1rem;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* Element Plus 组件样式定制 */
:deep(.el-pagination) {
  --el-pagination-bg-color: var(--bg-card);
  --el-pagination-text-color: var(--text-secondary);
  --el-pagination-border-radius: 8px;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.el-pager li) {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  margin: 0 4px;
  border-radius: 6px;
}

:deep(.el-pager li:hover) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.el-pager li.is-active) {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-title {
    font-size: 2rem;
  }

  .search-section {
    padding: 16px;
  }

  .model-actions {
    flex-direction: column;
  }

  .model-stats {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .stat-item {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .model-meta {
    align-items: flex-start;
  }

  .model-header {
    flex-direction: column;
    gap: 12px;
  }

  .pagination-section {
    overflow-x: auto;
  }
}
</style>
