# AIReady - 智能数据平台

AIReady 是一个专业的AI数据集和模型分享平台，为人工智能研究与应用提供强大支撑。

## 🚀 项目特色

- **现代科技主题设计** - 深色/蓝色调，科技感强烈
- **响应式布局** - 完美支持桌面端和移动端
- **丰富的功能模块** - 数据集管理、模型展示、个人中心等
- **优秀的用户体验** - 流畅的动画效果和交互设计

## 🛠️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **图标库**: @element-plus/icons-vue

## 📁 项目结构

```
AIReady/
├── src/
│   ├── components/          # 组件目录
│   │   └── Layout/         # 布局组件
│   │       ├── AppHeader.vue
│   │       └── AppFooter.vue
│   ├── views/              # 页面组件
│   │   ├── HomeView.vue    # 首页
│   │   ├── DatasetView.vue # 数据集列表页
│   │   ├── DatasetDetailView.vue # 数据集详情页
│   │   ├── ModelView.vue   # 模型页面
│   │   └── AccountView.vue # 个人中心
│   ├── router/             # 路由配置
│   ├── stores/             # 状态管理
│   ├── assets/             # 静态资源
│   │   └── main.css       # 全局样式
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── public/                 # 公共资源
└── package.json           # 项目配置
```

## 🎨 页面功能

### 首页 (Home)

- Hero 区域展示
- 平台统计数据
- 特色功能介绍
- CTA 行动号召

### 数据集页面 (Dataset)

- 数据集列表展示
- 搜索和筛选功能
- 分页功能
- 数据集详情页面

### 模型页面 (Model)

- AI模型展示
- 性能指标显示
- 下载和试用功能
- 分类筛选

### 个人中心 (Account)

- 用户信息管理
- 我的数据集列表
- 数据集上传功能
- 账户设置

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:5173

### 生产构建

```bash
npm run build
```

### 代码格式化

```bash
npm run format
```

## 🎯 设计理念

- **科技感设计** - 采用深色主题和渐变效果，营造未来科技感
- **用户体验优先** - 注重交互细节和动画效果
- **响应式设计** - 确保在各种设备上都有良好的显示效果
- **模块化开发** - 组件化设计，便于维护和扩展

## 📱 响应式支持

- **桌面端** (>= 1024px) - 完整功能展示
- **平板端** (768px - 1023px) - 适配中等屏幕
- **移动端** (< 768px) - 优化移动体验

## 🔧 自定义配置

项目使用 CSS 变量进行主题定制，可在 `src/assets/main.css` 中修改：

```css
:root {
  --primary-color: #409eff;
  --bg-primary: #0a0e27;
  --text-primary: #ffffff;
  /* 更多变量... */
}
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
