<template>
  <div class="dataset-detail-page">
    <div class="container">
      <!-- 返回按钮 -->
      <div class="back-section">
        <el-button @click="$router.back()" class="back-btn">
          <el-icon><ArrowLeft /></el-icon>
          返回数据集列表
        </el-button>
      </div>

      <!-- 数据集头部信息 -->
      <div class="dataset-header tech-card">
        <div class="header-content">
          <div class="dataset-info">
            <div class="dataset-icon">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="dataset-meta">
              <h1 class="dataset-title">{{ dataset.name }}</h1>
              <p class="dataset-subtitle">{{ dataset.description }}</p>
              <div class="dataset-badges">
                <el-tag type="primary" size="large">{{ dataset.category }}</el-tag>
                <el-tag type="info" size="large">{{ dataset.size }}</el-tag>
                <el-tag type="success" size="large">
                  <el-icon><Star /></el-icon>
                  {{ dataset.rating }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="dataset-actions">
            <el-button type="primary" size="large" class="tech-button">
              <el-icon><Download /></el-icon>
              下载数据集
            </el-button>
            <el-button size="large" class="tech-button secondary">
              <el-icon><Star /></el-icon>
              收藏
            </el-button>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="24">
          <el-col :xs="12" :sm="6">
            <div class="stat-card tech-card">
              <el-icon class="stat-icon"><Download /></el-icon>
              <div class="stat-content">
                <div class="stat-value">{{ dataset.downloads }}</div>
                <div class="stat-label">下载次数</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6">
            <div class="stat-card tech-card">
              <el-icon class="stat-icon"><View /></el-icon>
              <div class="stat-content">
                <div class="stat-value">{{ dataset.views }}</div>
                <div class="stat-label">浏览次数</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6">
            <div class="stat-card tech-card">
              <el-icon class="stat-icon"><Calendar /></el-icon>
              <div class="stat-content">
                <div class="stat-value">{{ dataset.uploadDate }}</div>
                <div class="stat-label">上传时间</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6">
            <div class="stat-card tech-card">
              <el-icon class="stat-icon"><User /></el-icon>
              <div class="stat-content">
                <div class="stat-value">{{ dataset.author }}</div>
                <div class="stat-label">上传者</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 详细信息 -->
      <el-row :gutter="24">
        <el-col :xs="24" :lg="16">
          <!-- 数据集描述 -->
          <div class="detail-section tech-card">
            <h2 class="section-title">数据集描述</h2>
            <div class="markdown-content" v-html="dataset.detailDescription"></div>
          </div>

          <!-- 文件结构 -->
          <div class="detail-section tech-card">
            <h2 class="section-title">文件结构</h2>
            <el-tree
              :data="dataset.fileStructure"
              :props="{ children: 'children', label: 'name' }"
              default-expand-all
              class="file-tree"
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.type === 'folder'"><Folder /></el-icon>
                  <el-icon v-else><Document /></el-icon>
                  <span>{{ data.name }}</span>
                  <span v-if="data.size" class="file-size">{{ data.size }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </el-col>

        <el-col :xs="24" :lg="8">
          <!-- 标签 -->
          <div class="detail-section tech-card">
            <h2 class="section-title">标签</h2>
            <div class="tags-container">
              <el-tag v-for="tag in dataset.tags" :key="tag" class="dataset-tag" size="large">
                {{ tag }}
              </el-tag>
            </div>
          </div>

          <!-- 使用许可 -->
          <div class="detail-section tech-card">
            <h2 class="section-title">使用许可</h2>
            <div class="license-info">
              <el-icon class="license-icon"><Document /></el-icon>
              <div>
                <div class="license-name">{{ dataset.license }}</div>
                <div class="license-description">{{ dataset.licenseDescription }}</div>
              </div>
            </div>
          </div>

          <!-- 相关数据集 -->
          <div class="detail-section tech-card">
            <h2 class="section-title">相关数据集</h2>
            <div class="related-datasets">
              <div
                v-for="related in dataset.relatedDatasets"
                :key="related.id"
                class="related-item"
                @click="$router.push(`/dataset/${related.id}`)"
              >
                <el-icon><FolderOpened /></el-icon>
                <span>{{ related.name }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import {
  ArrowLeft,
  FolderOpened,
  Download,
  Star,
  View,
  Calendar,
  User,
  Folder,
  Document,
} from '@element-plus/icons-vue'

const route = useRoute()
const datasetId = route.params.id

const dataset = ref({
  id: 1,
  name: 'ImageNet-1K',
  description: '包含1000个类别的大规模图像分类数据集，是计算机视觉领域的经典基准数据集。',
  category: '计算机视觉',
  size: '150GB',
  downloads: '125,432',
  views: '256,789',
  rating: '4.8',
  uploadDate: '2024-01-15',
  author: 'Stanford Vision Lab',
  tags: ['图像分类', '深度学习', '基准测试', '计算机视觉', '神经网络'],
  license: 'MIT License',
  licenseDescription: '允许商业和非商业使用，需要保留版权声明',
  detailDescription: `
    <h3>数据集概述</h3>
    <p>ImageNet-1K是一个大规模的图像分类数据集，包含1000个不同的类别，每个类别包含约1000张高质量的图像。该数据集是计算机视觉领域最重要的基准数据集之一。</p>
    
    <h3>数据集特点</h3>
    <ul>
      <li>包含1,281,167张训练图像</li>
      <li>50,000张验证图像</li>
      <li>100,000张测试图像</li>
      <li>1000个不同的类别</li>
      <li>图像分辨率不低于256x256像素</li>
    </ul>
    
    <h3>使用场景</h3>
    <p>该数据集广泛用于：</p>
    <ul>
      <li>图像分类模型训练</li>
      <li>深度学习算法验证</li>
      <li>计算机视觉研究</li>
      <li>模型性能基准测试</li>
    </ul>
  `,
  fileStructure: [
    {
      name: 'train',
      type: 'folder',
      children: [
        { name: 'n01440764', type: 'folder', size: '1.2GB' },
        { name: 'n01443537', type: 'folder', size: '1.1GB' },
        { name: '...', type: 'folder', size: '...' },
      ],
    },
    {
      name: 'val',
      type: 'folder',
      children: [
        { name: 'ILSVRC2012_val_00000001.JPEG', type: 'file', size: '79KB' },
        { name: 'ILSVRC2012_val_00000002.JPEG', type: 'file', size: '85KB' },
        { name: '...', type: 'file', size: '...' },
      ],
    },
    {
      name: 'test',
      type: 'folder',
      children: [
        { name: 'ILSVRC2012_test_00000001.JPEG', type: 'file', size: '92KB' },
        { name: 'ILSVRC2012_test_00000002.JPEG', type: 'file', size: '76KB' },
        { name: '...', type: 'file', size: '...' },
      ],
    },
    { name: 'README.md', type: 'file', size: '5KB' },
    { name: 'labels.txt', type: 'file', size: '12KB' },
  ],
  relatedDatasets: [
    { id: 2, name: 'CIFAR-10' },
    { id: 3, name: 'MNIST' },
    { id: 4, name: 'ImageNet-21K' },
  ],
})

onMounted(() => {
  // 根据路由参数加载对应的数据集信息
  console.log('Loading dataset:', datasetId)
})
</script>

<style scoped>
.dataset-detail-page {
  background: var(--bg-primary);
  min-height: 100vh;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.back-section {
  margin-bottom: 24px;
}

.back-btn {
  background: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
}

.back-btn:hover {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.dataset-header {
  margin-bottom: 32px;
  padding: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.dataset-info {
  display: flex;
  gap: 24px;
  flex: 1;
}

.dataset-icon {
  font-size: 4rem;
  color: var(--primary-color);
  flex-shrink: 0;
}

.dataset-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
  line-height: 1.2;
}

.dataset-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 20px;
  line-height: 1.6;
}

.dataset-badges {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.dataset-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-shrink: 0;
}

.stats-section {
  margin-bottom: 32px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  text-align: left;
}

.stat-icon {
  font-size: 2rem;
  color: var(--primary-color);
  flex-shrink: 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.detail-section {
  margin-bottom: 24px;
  padding: 24px;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--border-color);
}

.markdown-content {
  color: var(--text-secondary);
  line-height: 1.8;
}

.markdown-content h3 {
  color: var(--text-primary);
  font-size: 1.25rem;
  margin: 24px 0 12px 0;
}

.markdown-content ul {
  padding-left: 20px;
}

.markdown-content li {
  margin-bottom: 8px;
}

.file-tree {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
}

.file-size {
  margin-left: auto;
  font-size: 0.875rem;
  color: var(--text-muted);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.dataset-tag {
  background: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
  padding: 8px 16px !important;
}

.license-info {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.license-icon {
  font-size: 2rem;
  color: var(--primary-color);
  flex-shrink: 0;
}

.license-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.license-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

.related-datasets {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.related-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.related-item:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateX(4px);
}

/* Element Plus 组件样式定制 */
:deep(.el-tree) {
  background: transparent;
  color: var(--text-secondary);
}

:deep(.el-tree-node__content) {
  background: transparent;
  color: var(--text-secondary);
}

:deep(.el-tree-node__content:hover) {
  background: var(--bg-secondary);
}

:deep(.el-tree-node__expand-icon) {
  color: var(--text-muted);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 24px;
  }

  .dataset-info {
    flex-direction: column;
    gap: 16px;
  }

  .dataset-title {
    font-size: 2rem;
  }

  .dataset-actions {
    flex-direction: row;
    width: 100%;
  }

  .dataset-actions .el-button {
    flex: 1;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .dataset-badges {
    flex-direction: column;
    align-items: flex-start;
  }

  .dataset-actions {
    flex-direction: column;
  }

  .tags-container {
    flex-direction: column;
  }
}
</style>
