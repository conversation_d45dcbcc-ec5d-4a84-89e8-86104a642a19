<template>
  <el-header class="app-header">
    <div class="header-content">
      <!-- Logo 区域 -->
      <div class="logo-section">
        <router-link to="/" class="logo-link">
          <div class="logo">
            <el-icon class="logo-icon"><DataAnalysis /></el-icon>
            <span class="logo-text gradient-text">AIReady</span>
          </div>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <el-menu
        :default-active="activeIndex"
        class="header-menu"
        mode="horizontal"
        @select="handleSelect"
      >
        <el-menu-item index="/">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </el-menu-item>
        <el-menu-item index="/dataset">
          <el-icon><FolderOpened /></el-icon>
          <span>数据集</span>
        </el-menu-item>
        <el-menu-item index="/model">
          <el-icon><Cpu /></el-icon>
          <span>模型</span>
        </el-menu-item>
        <el-menu-item index="/account">
          <el-icon><User /></el-icon>
          <span>个人中心</span>
        </el-menu-item>
      </el-menu>

      <!-- 用户操作区域 -->
      <div class="user-actions">
        <el-button type="primary" class="tech-button">
          <el-icon><Plus /></el-icon>
          上传数据集
        </el-button>
      </div>
    </div>
  </el-header>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  DataAnalysis,
  House,
  FolderOpened,
  Cpu,
  User,
  Plus
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const activeIndex = computed(() => route.path)

const handleSelect = (key) => {
  router.push(key)
}
</script>

<style scoped>
.app-header {
  height: 70px !important;
  padding: 0 !important;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 10px var(--shadow-color);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.logo-section {
  flex-shrink: 0;
}

.logo-link {
  text-decoration: none;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 32px;
  color: var(--primary-color);
}

.logo-text {
  font-size: 24px;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-menu {
  flex: 1;
  justify-content: center;
  background: transparent !important;
  border: none !important;
}

.header-menu .el-menu-item {
  color: var(--text-secondary) !important;
  font-weight: 500;
  border-bottom: 2px solid transparent !important;
  transition: all 0.3s ease;
}

.header-menu .el-menu-item:hover,
.header-menu .el-menu-item.is-active {
  color: var(--primary-color) !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border-bottom-color: var(--primary-color) !important;
}

.user-actions {
  flex-shrink: 0;
}

.tech-button {
  background: var(--gradient-primary) !important;
  border: none !important;
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.tech-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .logo-text {
    font-size: 20px;
  }
  
  .header-menu .el-menu-item span {
    display: none;
  }
  
  .tech-button {
    padding: 8px 16px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .user-actions .tech-button span {
    display: none;
  }
}
</style>
