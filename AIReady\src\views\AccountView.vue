<template>
  <div class="account-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title gradient-text">个人中心</h1>
        <p class="page-subtitle">管理您的数据集、模型和个人信息</p>
      </div>

      <!-- 用户信息卡片 -->
      <div class="user-info-section">
        <div class="user-card tech-card">
          <div class="user-avatar">
            <el-avatar :size="80" class="avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </div>
          <div class="user-details">
            <h2 class="user-name">{{ userInfo.name }}</h2>
            <p class="user-email">{{ userInfo.email }}</p>
            <div class="user-stats">
              <div class="stat-item">
                <span class="stat-value">{{ userInfo.datasetsCount }}</span>
                <span class="stat-label">数据集</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ userInfo.modelsCount }}</span>
                <span class="stat-label">模型</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ userInfo.downloadsCount }}</span>
                <span class="stat-label">下载量</span>
              </div>
            </div>
          </div>
          <div class="user-actions">
            <el-button type="primary" class="tech-button">
              <el-icon><Edit /></el-icon>
              编辑资料
            </el-button>
          </div>
        </div>
      </div>

      <!-- 功能标签页 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab" class="account-tabs">
          <el-tab-pane label="我的数据集" name="datasets">
            <div class="tab-content">
              <div class="section-header">
                <h3>我的数据集</h3>
                <el-button type="primary" class="tech-button" @click="showUploadDialog = true">
                  <el-icon><Plus /></el-icon>
                  上传数据集
                </el-button>
              </div>

              <div class="datasets-grid">
                <el-row :gutter="24">
                  <el-col
                    v-for="dataset in userDatasets"
                    :key="dataset.id"
                    :xs="24"
                    :sm="12"
                    :lg="8"
                  >
                    <div class="dataset-card tech-card">
                      <div class="card-header">
                        <h4 class="card-title">{{ dataset.name }}</h4>
                        <el-dropdown>
                          <el-button circle size="small">
                            <el-icon><MoreFilled /></el-icon>
                          </el-button>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item @click="editDataset(dataset)">
                                <el-icon><Edit /></el-icon>
                                编辑
                              </el-dropdown-item>
                              <el-dropdown-item @click="shareDataset(dataset)">
                                <el-icon><Share /></el-icon>
                                分享
                              </el-dropdown-item>
                              <el-dropdown-item @click="deleteDataset(dataset)" divided>
                                <el-icon><Delete /></el-icon>
                                删除
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                      <p class="card-description">{{ dataset.description }}</p>
                      <div class="card-stats">
                        <span class="stat">
                          <el-icon><Download /></el-icon>
                          {{ dataset.downloads }}
                        </span>
                        <span class="stat">
                          <el-icon><View /></el-icon>
                          {{ dataset.views }}
                        </span>
                        <span class="stat">
                          <el-icon><Calendar /></el-icon>
                          {{ dataset.uploadDate }}
                        </span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="我的模型" name="models">
            <div class="tab-content">
              <div class="section-header">
                <h3>我的模型</h3>
                <el-button type="primary" class="tech-button">
                  <el-icon><Plus /></el-icon>
                  上传模型
                </el-button>
              </div>

              <div class="models-grid">
                <el-row :gutter="24">
                  <el-col v-for="model in userModels" :key="model.id" :xs="24" :sm="12" :lg="8">
                    <div class="model-card tech-card">
                      <div class="card-header">
                        <h4 class="card-title">{{ model.name }}</h4>
                        <el-dropdown>
                          <el-button circle size="small">
                            <el-icon><MoreFilled /></el-icon>
                          </el-button>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item>
                                <el-icon><Edit /></el-icon>
                                编辑
                              </el-dropdown-item>
                              <el-dropdown-item>
                                <el-icon><Share /></el-icon>
                                分享
                              </el-dropdown-item>
                              <el-dropdown-item divided>
                                <el-icon><Delete /></el-icon>
                                删除
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                      <p class="card-description">{{ model.description }}</p>
                      <div class="model-performance">
                        <span class="performance">准确率: {{ model.accuracy }}</span>
                        <span class="performance">速度: {{ model.speed }}</span>
                      </div>
                      <div class="card-stats">
                        <span class="stat">
                          <el-icon><Download /></el-icon>
                          {{ model.downloads }}
                        </span>
                        <span class="stat">
                          <el-icon><Star /></el-icon>
                          {{ model.rating }}
                        </span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="账户设置" name="settings">
            <div class="tab-content">
              <div class="settings-section">
                <h3>账户设置</h3>
                <el-form :model="settingsForm" label-width="120px" class="settings-form">
                  <el-form-item label="用户名">
                    <el-input v-model="settingsForm.username" />
                  </el-form-item>
                  <el-form-item label="邮箱">
                    <el-input v-model="settingsForm.email" />
                  </el-form-item>
                  <el-form-item label="个人简介">
                    <el-input
                      v-model="settingsForm.bio"
                      type="textarea"
                      :rows="4"
                      placeholder="介绍一下自己..."
                    />
                  </el-form-item>
                  <el-form-item label="通知设置">
                    <el-switch
                      v-model="settingsForm.emailNotifications"
                      active-text="接收邮件通知"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" class="tech-button"> 保存设置 </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 上传数据集对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传数据集" width="600px" class="upload-dialog">
      <el-form :model="uploadForm" label-width="100px">
        <el-form-item label="数据集名称" required>
          <el-input v-model="uploadForm.name" placeholder="请输入数据集名称" />
        </el-form-item>
        <el-form-item label="类别" required>
          <el-select v-model="uploadForm.category" placeholder="选择类别" style="width: 100%">
            <el-option label="计算机视觉" value="cv" />
            <el-option label="自然语言处理" value="nlp" />
            <el-option label="语音识别" value="speech" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            :rows="3"
            placeholder="请描述数据集的内容和用途"
          />
        </el-form-item>
        <el-form-item label="文件上传">
          <el-upload class="upload-demo" drag action="#" multiple :auto-upload="false">
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip">支持 zip, tar.gz 等压缩格式，单个文件不超过 2GB</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button type="primary" class="tech-button" @click="handleUpload">
            上传数据集
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User,
  Edit,
  Plus,
  MoreFilled,
  Share,
  Delete,
  Download,
  View,
  Calendar,
  Star,
  UploadFilled,
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('datasets')
const showUploadDialog = ref(false)

const userInfo = ref({
  name: 'AI研究员',
  email: '<EMAIL>',
  datasetsCount: 12,
  modelsCount: 8,
  downloadsCount: '25.6K',
})

const userDatasets = ref([
  {
    id: 1,
    name: '图像分类数据集',
    description: '包含10000张高质量图像的分类数据集',
    downloads: '1,234',
    views: '5,678',
    uploadDate: '2024-01-15',
  },
  {
    id: 2,
    name: '文本情感分析',
    description: '中文情感分析数据集，包含正面和负面评论',
    downloads: '892',
    views: '3,456',
    uploadDate: '2024-01-20',
  },
])

const userModels = ref([
  {
    id: 1,
    name: 'ResNet-Custom',
    description: '基于ResNet改进的图像分类模型',
    accuracy: '92.5%',
    speed: '15ms',
    downloads: '567',
    rating: '4.8',
  },
])

const settingsForm = ref({
  username: 'AI研究员',
  email: '<EMAIL>',
  bio: '专注于计算机视觉和深度学习研究',
  emailNotifications: true,
})

const uploadForm = ref({
  name: '',
  category: '',
  description: '',
})

// 方法
const editDataset = (dataset) => {
  ElMessage.info(`编辑数据集: ${dataset.name}`)
}

const shareDataset = (dataset) => {
  ElMessage.success(`分享链接已复制: ${dataset.name}`)
}

const deleteDataset = (dataset) => {
  ElMessage.warning(`删除数据集: ${dataset.name}`)
}

const handleUpload = () => {
  ElMessage.success('数据集上传成功！')
  showUploadDialog.value = false
  // 重置表单
  uploadForm.value = {
    name: '',
    category: '',
    description: '',
  }
}
</script>

<style scoped>
.account-page {
  background: var(--bg-primary);
  min-height: 100vh;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
}

.user-info-section {
  margin-bottom: 32px;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 32px;
}

.user-avatar .avatar {
  background: var(--gradient-primary);
  color: white;
  font-size: 2rem;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.user-email {
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.user-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.tabs-section {
  margin-bottom: 32px;
}

.account-tabs {
  background: var(--bg-card);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid var(--border-color);
}

.tab-content {
  padding-top: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.datasets-grid,
.models-grid {
  margin-bottom: 24px;
}

.dataset-card,
.model-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
  margin-right: 12px;
}

.card-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 16px;
  flex: 1;
}

.model-performance {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.performance {
  display: block;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.performance:last-child {
  margin-bottom: 0;
}

.card-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-muted);
  font-size: 0.875rem;
}

.settings-section {
  max-width: 600px;
}

.settings-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24px;
}

.settings-form {
  background: var(--bg-tertiary);
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

/* Element Plus 组件样式定制 */
:deep(.el-tabs__header) {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  color: var(--text-secondary);
  border-radius: 6px;
  padding: 12px 20px;
  margin-right: 8px;
  border: none;
}

:deep(.el-tabs__item:hover) {
  color: var(--primary-color);
  background: rgba(64, 158, 255, 0.1);
}

:deep(.el-tabs__item.is-active) {
  color: var(--primary-color);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
}

:deep(.el-tabs__active-bar) {
  display: none;
}

:deep(.el-form-item__label) {
  color: var(--text-primary);
}

:deep(.el-dialog) {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
}

:deep(.el-dialog__header) {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  color: var(--text-primary);
  font-weight: 600;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-upload-dragger) {
  background: var(--bg-tertiary);
  border: 2px dashed var(--border-color);
  border-radius: 8px;
}

:deep(.el-upload-dragger:hover) {
  border-color: var(--primary-color);
}

:deep(.el-upload__text) {
  color: var(--text-secondary);
}

:deep(.el-upload__tip) {
  color: var(--text-muted);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-title {
    font-size: 2rem;
  }

  .user-card {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .user-stats {
    justify-content: center;
    gap: 24px;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .card-stats {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .stat {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .user-stats {
    flex-direction: column;
    gap: 16px;
  }

  .account-tabs {
    padding: 16px;
  }

  .tab-content {
    padding-top: 16px;
  }
}
</style>
