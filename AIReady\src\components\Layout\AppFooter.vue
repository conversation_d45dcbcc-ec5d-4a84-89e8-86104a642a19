<template>
  <el-footer class="app-footer">
    <div class="footer-content">
      <div class="footer-section">
        <div class="footer-logo">
          <el-icon class="logo-icon"><DataAnalysis /></el-icon>
          <span class="logo-text gradient-text">AIReady</span>
        </div>
        <p class="footer-description">
          专业的AI数据集和模型分享平台，助力人工智能研究与应用发展。
        </p>
      </div>

      <div class="footer-section">
        <h4>快速链接</h4>
        <ul class="footer-links">
          <li><router-link to="/">首页</router-link></li>
          <li><router-link to="/dataset">数据集</router-link></li>
          <li><router-link to="/model">模型</router-link></li>
          <li><router-link to="/account">个人中心</router-link></li>
        </ul>
      </div>

      <div class="footer-section">
        <h4>帮助与支持</h4>
        <ul class="footer-links">
          <li><a href="#" @click.prevent>使用指南</a></li>
          <li><a href="#" @click.prevent>API 文档</a></li>
          <li><a href="#" @click.prevent>常见问题</a></li>
          <li><a href="#" @click.prevent>联系我们</a></li>
        </ul>
      </div>

      <div class="footer-section">
        <h4>关注我们</h4>
        <div class="social-links">
          <el-button circle class="social-btn">
            <el-icon><Message /></el-icon>
          </el-button>
          <el-button circle class="social-btn">
            <el-icon><Share /></el-icon>
          </el-button>
          <el-button circle class="social-btn">
            <el-icon><Star /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <div class="footer-bottom-content">
        <p>&copy; 2024 AIReady. All rights reserved.</p>
        <div class="footer-bottom-links">
          <a href="#" @click.prevent>隐私政策</a>
          <span>|</span>
          <a href="#" @click.prevent>服务条款</a>
          <span>|</span>
          <a href="#" @click.prevent>免责声明</a>
        </div>
      </div>
    </div>
  </el-footer>
</template>

<script setup>
import { DataAnalysis, Message, Share, Star } from '@element-plus/icons-vue'
</script>

<style scoped>
.app-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: 0 !important;
  height: auto !important;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 48px 24px 24px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
}

.footer-section h4 {
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 28px;
  color: var(--primary-color);
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
}

.footer-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--primary-color);
}

.social-links {
  display: flex;
  gap: 12px;
}

.social-btn {
  background: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
  transition: all 0.3s ease;
}

.social-btn:hover {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
  transform: translateY(-2px);
}

.footer-bottom {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: 16px 0;
}

.footer-bottom-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-muted);
  font-size: 14px;
}

.footer-bottom-links {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-bottom-links a {
  color: var(--text-muted);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    padding: 32px 16px 16px;
    gap: 24px;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    gap: 12px;
    padding: 0 16px;
  }
}
</style>
